<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="basic">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户级别">
            {{ form.customerLevel === '1' ? '集团' : '公司' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属集团" v-if="form.customerLevel === '2'">
            {{ form.parent?.customerName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ form.customerName }}
          </el-descriptions-item>
          <el-descriptions-item label="客户类型">
            {{ form.customerType }}
          </el-descriptions-item>
          <el-descriptions-item label="客户来源">
            {{ form.customerSource }}
          </el-descriptions-item>
          <el-descriptions-item label="客户重要性">
            {{ form.customerImportance }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="form.status === '0' ? 'success' : 'danger'">
              {{ form.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所在地区" :span="2">
            {{ form.province }} {{ form.city }} {{ form.district }}
          </el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">
            {{ form.address }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ form.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="联系人信息" name="contacts">
        <el-table :data="form.contacts" style="margin-top: 10px;">
          <el-table-column label="联系人姓名" align="center" prop="contactName" />
          <el-table-column label="职务" align="center" prop="position" />
          <el-table-column label="电话" align="center" prop="phone" />
          <el-table-column label="微信" align="center" prop="wechat" />
          <el-table-column label="邮箱" align="center" prop="email" />
          <el-table-column label="主要联系人" align="center" prop="isPrimary" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isPrimary === '1' ? 'success' : 'info'">
                {{ scope.row.isPrimary === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="内部负责人" name="managers">
        <el-table :data="form.internalManagers" style="margin-top: 10px;">
          <el-table-column label="负责人" align="center">
            <template #default="scope">
              {{ scope.row.user?.nickName || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="主要负责人" align="center" prop="isPrimary" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isPrimary === '1' ? 'success' : 'info'">
                {{ scope.row.isPrimary === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="跟进记录" name="followRecords">
        <el-form :model="followRecordQuery" :inline="true" style="margin-bottom: 10px;">
          <el-form-item label="跟进内容">
            <el-input
              v-model="followRecordQuery.followContent"
              placeholder="请输入跟进内容"
              clearable
              style="width: 180px"
              @keyup.enter="handleFollowRecordQuery"
            />
          </el-form-item>
          <el-form-item label="跟进人">
            <el-select
              v-model="followRecordQuery.followUserId"
              placeholder="请选择跟进人"
              clearable
              filterable
              style="width: 150px"
              @visible-change="getUserListForSearch"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.userId"
                :label="user.nickName"
                :value="user.userId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="跟进时间">
            <el-date-picker
              v-model="followRecordDateRange"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleFollowRecordQuery">搜索</el-button>
            <el-button type="success" icon="Plus" @click="handleAddFollowRecord">新增跟进</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="followRecordList" v-loading="followRecordLoading">
          <el-table-column label="跟进时间" align="center" width="160">
            <template #default="scope">
              {{ parseTime(scope.row.followTime) }}
            </template>
          </el-table-column>
          <el-table-column label="跟进内容" align="center" prop="followContent" show-overflow-tooltip />
          <el-table-column label="跟进人" align="center" width="100">
            <template #default="scope">
              <span>{{ getUserNameById(scope.row.followUserId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" width="100">
            <template #default="scope">
              <span>{{ getUserNameById(scope.row.createUserId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" width="160">
            <template #default="scope">
              {{ parseTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="更新人" align="center" width="100">
            <template #default="scope">
              <span>{{ getUserNameById(scope.row.updateUserId) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" align="center" width="160">
            <template #default="scope">
              {{ parseTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" icon="Edit" @click="handleEditFollowRecord(scope.row)">编辑</el-button>
              <el-button link type="danger" icon="Delete" @click="handleDeleteFollowRecord(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="followRecordTotal > 0"
          :total="followRecordTotal"
          v-model:page="followRecordQuery.pageNum"
          v-model:limit="followRecordQuery.pageSize"
          @pagination="getFollowRecordList"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 新增/编辑跟进记录弹窗 -->
  <el-dialog :title="followRecordTitle" v-model="followRecordDialogVisible" width="600px" append-to-body>
    <el-form ref="followRecordFormRef" :model="followRecordForm" :rules="followRecordRules" label-width="100px">
      <el-form-item label="跟进时间" prop="followTime">
        <el-date-picker
          v-model="followRecordForm.followTime"
          type="datetime"
          placeholder="选择跟进时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="跟进人" prop="followUserId">
        <el-select
          v-model="followRecordForm.followUserId"
          placeholder="请选择跟进人"
          style="width: 100%"
          filterable
          clearable
          @visible-change="getUserList"
        >
          <el-option
            v-for="user in userList"
            :key="user.userId"
            :label="user.nickName"
            :value="user.userId"
          >
            <span>{{ user.nickName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ user.userName }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="跟进内容" prop="followContent">
        <el-input
          v-model="followRecordForm.followContent"
          type="textarea"
          :rows="4"
          placeholder="请输入跟进内容"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelFollowRecord">取 消</el-button>
        <el-button type="primary" @click="submitFollowRecord">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, ref } from 'vue';
import { pageCustomerFollowRecord, addCustomerFollowRecord, updateCustomerFollowRecord, delCustomerFollowRecord } from '@/api/customer/followRecord';
import { listUser } from '@/api/system/user';
import useUserStore from '@/store/modules/user';

const props = defineProps({
  title: {
    type: String,
    default: "客户详情"
  },
  open: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["cancel"]);
const { proxy } = getCurrentInstance();
const userStore = useUserStore();

const dialogVisible = computed({
  get: () => props.open,
  set: (val) => {
    if (!val) {
      emit("cancel");
    }
  }
});

const activeName = ref("basic");
const form = ref({
  customerId: undefined,
  customerName: undefined,
  customerLevel: undefined,
  parentId: undefined,
  customerType: undefined,
  customerSource: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  address: undefined,
  status: undefined,
  remark: undefined,
  customerImportance: undefined,
  contacts: [],
  internalManagers: [],
  parent: undefined
});

// 跟进记录相关数据
const followRecordList = ref([]);
const followRecordLoading = ref(false);
const followRecordTotal = ref(0);
const followRecordQuery = reactive({
  customerId: undefined,
  followContent: undefined,
  followUserId: undefined,
  pageNum: 1,
  pageSize: 10
});
const followRecordDateRange = ref([]);

// 跟进记录弹窗相关
const followRecordDialogVisible = ref(false);
const followRecordTitle = ref("");
const followRecordFormRef = ref();
const followRecordForm = ref({
  id: undefined,
  customerId: undefined,
  followContent: undefined,
  followTime: undefined,
  followUserId: undefined
});

const followRecordRules = reactive({
  followTime: [
    { required: true, message: "跟进时间不能为空", trigger: "blur" }
  ],
  followUserId: [
    { required: true, message: "跟进人不能为空", trigger: "change" }
  ],
  followContent: [
    { required: true, message: "跟进内容不能为空", trigger: "blur" }
  ]
});

// 用户列表
const userList = ref([]);
const userOptions = ref([]);

// 用户ID到用户名称的映射
const userIdToName = ref({});

// 根据用户ID获取用户名称
function getUserNameById(userId) {
  if (!userId) return "";
  return userIdToName.value[userId] || userId;
}

// 获取用户列表（用于新增/编辑跟进记录）
function getUserList(open) {
  if (open) {
    userList.value = [];
    listUser({ status: '0' }).then(response => {
      userList.value = response.rows || [];
      // 更新用户ID到名称的映射
      response.rows.forEach(user => {
        userIdToName.value[user.userId] = user.nickName;
      });
    });
  }
}

// 获取用户列表（用于搜索下拉框）
function getUserListForSearch(open) {
  if (open) {
    userOptions.value = [];
    listUser({ status: '0' }).then(response => {
      userOptions.value = response.rows || [];
      // 更新用户ID到名称的映射
      response.rows.forEach(user => {
        userIdToName.value[user.userId] = user.nickName;
      });
    });
  }
}

// 加载用户名称
function loadUserNames() {
  // 收集所有用户ID
  const userIds = new Set();
  followRecordList.value.forEach(record => {
    if (record.followUserId) userIds.add(record.followUserId);
    if (record.createUserId) userIds.add(record.createUserId);
    if (record.updateUserId) userIds.add(record.updateUserId);
  });

  // 获取用户信息
  userIds.forEach(userId => {
    if (!userIdToName.value[userId]) {
      listUser({ userId: userId }).then(response => {
        if (response.rows && response.rows.length > 0) {
          userIdToName.value[userId] = response.rows[0].nickName;
        }
      });
    }
  });
}

// 设置表单数据
function setFormData(data) {
  console.log("设置详情数据:", JSON.stringify(data));
  // 深拷贝数据，避免引用问题
  const newData = JSON.parse(JSON.stringify(data));
  // 设置表单数据
  form.value = newData;
  // 设置跟进记录查询的客户ID
  followRecordQuery.customerId = newData.customerId;
  // 加载跟进记录
  getFollowRecordList();
  console.log("设置后的详情数据:", JSON.stringify(form.value));
}

// 获取跟进记录列表
function getFollowRecordList() {
  if (!followRecordQuery.customerId) return;

  followRecordLoading.value = true;
  const queryParams = { ...followRecordQuery };

  // 处理时间范围
  if (followRecordDateRange.value && followRecordDateRange.value.length === 2) {
    queryParams.beginTime = followRecordDateRange.value[0];
    queryParams.endTime = followRecordDateRange.value[1];
  }

  pageCustomerFollowRecord(queryParams).then(response => {
    followRecordList.value = response.data?.rows || [];
    followRecordTotal.value = response.data?.total || 0;
    followRecordLoading.value = false;
    // 加载用户名称
    loadUserNames();
  }).catch(() => {
    followRecordLoading.value = false;
  });
}

// 搜索跟进记录
function handleFollowRecordQuery() {
  followRecordQuery.pageNum = 1;
  getFollowRecordList();
}

// 新增跟进记录
function handleAddFollowRecord() {
  resetFollowRecordForm();
  followRecordForm.value.customerId = form.value.customerId;
  followRecordForm.value.followTime = proxy.parseTime(new Date());
  // 设置默认跟进人为当前用户
  followRecordForm.value.followUserId = userStore.id;
  followRecordTitle.value = "新增跟进记录";
  followRecordDialogVisible.value = true;
}

// 编辑跟进记录
function handleEditFollowRecord(row) {
  resetFollowRecordForm();
  followRecordForm.value = { ...row };
  followRecordTitle.value = "编辑跟进记录";
  followRecordDialogVisible.value = true;
}

// 删除跟进记录
function handleDeleteFollowRecord(row) {
  proxy.$modal.confirm('是否确认删除该跟进记录？').then(() => {
    return delCustomerFollowRecord(row.id);
  }).then(() => {
    getFollowRecordList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// 重置跟进记录表单
function resetFollowRecordForm() {
  followRecordForm.value = {
    id: undefined,
    customerId: undefined,
    followContent: undefined,
    followTime: undefined,
    followUserId: undefined
  };
  if (followRecordFormRef.value) {
    followRecordFormRef.value.resetFields();
  }
}

// 取消跟进记录弹窗
function cancelFollowRecord() {
  followRecordDialogVisible.value = false;
  resetFollowRecordForm();
}

// 提交跟进记录
function submitFollowRecord() {
  followRecordFormRef.value.validate(valid => {
    if (valid) {
      const formData = { ...followRecordForm.value };

      if (formData.id) {
        // 编辑
        updateCustomerFollowRecord(formData).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          followRecordDialogVisible.value = false;
          getFollowRecordList();
        });
      } else {
        // 新增
        addCustomerFollowRecord(formData).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          followRecordDialogVisible.value = false;
          getFollowRecordList();
        });
      }
    }
  });
}

// 取消按钮
function cancel() {
  emit("cancel");
}

// 对外暴露方法
defineExpose({
  setFormData,
  form
});
</script>
