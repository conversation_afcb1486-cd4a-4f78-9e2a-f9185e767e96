from datetime import datetime
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Union, Dict, Any
from config.constant import CommonConstant
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_customer.dao.customer_follow_record_dao import CustomerFollowRecordDao
from module_customer.entity.do.customer_follow_record_do import CustomerFollowRecord
from module_customer.entity.vo.customer_follow_record_vo import (
    AddCustomerFollowRecordModel,
    CustomerFollowRecordModel,
    CustomerFollowRecordPageQueryModel,
    CustomerFollowRecordQueryModel,
    EditCustomerFollowRecordModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.dao.user_dao import UserDao


class CustomerFollowRecordService:
    """
    客户跟进记录服务层
    """

    @classmethod
    async def get_customer_follow_record_list_services(cls, query_db: AsyncSession, query_object: CustomerFollowRecordQueryModel):
        """
        获取客户跟进记录列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 客户跟进记录列表
        """
        follow_record_list = await CustomerFollowRecordDao.get_customer_follow_record_list(query_db, query_object)
        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(follow_record_list)

    @classmethod
    async def get_customer_follow_record_page_list_services(cls, query_db: AsyncSession, query_object: CustomerFollowRecordPageQueryModel):
        """
        获取客户跟进记录分页列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 客户跟进记录分页列表
        """
        follow_record_page_object = await CustomerFollowRecordDao.get_customer_follow_record_page_list(query_db, query_object)

        # 手动添加用户信息
        if follow_record_page_object.rows:
            # 收集所有用户ID
            user_ids = set()
            for record in follow_record_page_object.rows:
                if hasattr(record, 'follow_user_id') and record.follow_user_id:
                    user_ids.add(record.follow_user_id)
                if hasattr(record, 'create_user_id') and record.create_user_id:
                    user_ids.add(record.create_user_id)

            # 批量查询用户信息
            users_dict = {}
            if user_ids:
                from module_admin.dao.user_dao import UserDao
                for user_id in user_ids:
                    user = await UserDao.get_user_detail_by_id(query_db, user_id)
                    if user:
                        users_dict[user_id] = CamelCaseUtil.transform_result(user)

            # 为每个记录添加用户信息
            processed_rows = []
            for record in follow_record_page_object.rows:
                record_dict = CamelCaseUtil.transform_result(record)
                if hasattr(record, 'follow_user_id') and record.follow_user_id in users_dict:
                    record_dict['followUser'] = users_dict[record.follow_user_id]
                if hasattr(record, 'create_user_id') and record.create_user_id in users_dict:
                    record_dict['createUser'] = users_dict[record.create_user_id]
                processed_rows.append(record_dict)

            # 替换原记录列表
            follow_record_page_object.rows = processed_rows

        return follow_record_page_object

    @classmethod
    async def get_customer_follow_record_detail_services(cls, query_db: AsyncSession, record_id: int):
        """
        获取客户跟进记录详情

        :param query_db: orm对象
        :param record_id: 跟进记录ID
        :return: 客户跟进记录详情
        """
        follow_record = await CustomerFollowRecordDao.get_customer_follow_record_detail_by_id(query_db, record_id)
        if follow_record:
            # 将下划线命名转换为驼峰命名
            return CamelCaseUtil.transform_result(follow_record)
        else:
            raise ServiceException(message='跟进记录不存在')

    @classmethod
    async def add_customer_follow_record_services(cls, query_db: AsyncSession, request: Request, add_follow_record: AddCustomerFollowRecordModel, current_user: CurrentUserModel):
        """
        新增客户跟进记录

        :param query_db: orm对象
        :param request: 请求对象
        :param add_follow_record: 新增客户跟进记录对象
        :param current_user: 当前用户对象
        :return: 新增结果
        """
        # 使用指定的跟进人用户ID
        follow_user_id = add_follow_record.follow_user_id

        # 创建跟进记录对象
        add_follow_record_do = CustomerFollowRecord(
            customer_id=add_follow_record.customer_id,
            follow_content=add_follow_record.follow_content,
            follow_time=add_follow_record.follow_time,
            follow_user_id=follow_user_id,
            create_user_id=current_user.user.user_id,
            create_by=current_user.user.user_name,
            create_time=datetime.now(),
            update_by=current_user.user.user_name,
            update_time=datetime.now()
        )

        # 新增跟进记录
        await CustomerFollowRecordDao.add_customer_follow_record_dao(query_db, add_follow_record_do)
        await query_db.commit()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(add_follow_record_do)

    @classmethod
    async def edit_customer_follow_record_services(cls, query_db: AsyncSession, request: Request, edit_follow_record: EditCustomerFollowRecordModel, current_user: CurrentUserModel):
        """
        编辑客户跟进记录

        :param query_db: orm对象
        :param request: 请求对象
        :param edit_follow_record: 编辑客户跟进记录对象
        :param current_user: 当前用户对象
        :return: 编辑结果
        """
        # 获取原跟进记录
        edit_follow_record_do = await CustomerFollowRecordDao.get_customer_follow_record_detail_by_id(query_db, edit_follow_record.id)
        if edit_follow_record_do:
            # 使用指定的跟进人用户ID
            follow_user_id = edit_follow_record.follow_user_id

            # 更新跟进记录信息
            edit_follow_record_do.customer_id = edit_follow_record.customer_id
            edit_follow_record_do.follow_content = edit_follow_record.follow_content
            edit_follow_record_do.follow_time = edit_follow_record.follow_time
            edit_follow_record_do.follow_user_id = follow_user_id
            edit_follow_record_do.update_by = current_user.user.user_name
            edit_follow_record_do.update_time = datetime.now()

            # 编辑跟进记录
            await CustomerFollowRecordDao.edit_customer_follow_record_dao(query_db, edit_follow_record_do)
            await query_db.commit()

            # 将下划线命名转换为驼峰命名
            return CamelCaseUtil.transform_result(edit_follow_record_do)
        else:
            raise ServiceException(message='跟进记录不存在')

    @classmethod
    async def delete_customer_follow_record_services(cls, query_db: AsyncSession, request: Request, record_ids: str, current_user: CurrentUserModel):
        """
        删除客户跟进记录

        :param query_db: orm对象
        :param request: 请求对象
        :param record_ids: 跟进记录ID字符串
        :param current_user: 当前用户对象
        :return: 删除结果
        """
        # 处理跟进记录ID
        record_id_list = record_ids.split(',')
        record_id_list = [int(record_id) for record_id in record_id_list if record_id.strip()]

        if not record_id_list:
            raise ServiceException(message='请选择要删除的跟进记录')

        # 删除跟进记录
        delete_count = await CustomerFollowRecordDao.delete_customer_follow_record_dao(query_db, record_id_list)
        await query_db.commit()

        return CrudResponseModel(is_success=True, message=f'删除成功，共删除{delete_count}条记录')
