from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.database import get_db
from module_admin.service.login_service import LoginService
from module_customer.entity.vo.customer_follow_record_vo import (
    AddCustomerFollowRecordModel,
    CustomerFollowRecordModel,
    CustomerFollowRecordPageQueryModel,
    CustomerFollowRecordQueryModel,
    EditCustomerFollowRecordModel,
)
from module_customer.service.customer_follow_record_service import CustomerFollowRecordService
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from module_admin.entity.vo.user_vo import CurrentUserModel

# 创建路由
router = APIRouter(prefix='/customer/follow-record', tags=['客户跟进记录管理'])


@router.get('/list', response_model=List[CustomerFollowRecordModel], summary='获取客户跟进记录列表')
async def get_customer_follow_record_list(
    request: Request,
    query_params: CustomerFollowRecordQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户跟进记录列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户跟进记录列表
    """
    follow_record_list = await CustomerFollowRecordService.get_customer_follow_record_list_services(db, query_params)
    return ResponseUtil.success(data=follow_record_list)


@router.get('/page', response_model=PageResponseModel, summary='获取客户跟进记录分页列表')
async def get_customer_follow_record_page(
    request: Request,
    query_params: CustomerFollowRecordPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户跟进记录分页列表

    :param request: 请求对象
    :param query_params: 分页查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户跟进记录分页列表
    """
    follow_record_page = await CustomerFollowRecordService.get_customer_follow_record_page_list_services(db, query_params)
    return ResponseUtil.success(data=follow_record_page)


@router.get('/{record_id}', response_model=CustomerFollowRecordModel, summary='获取客户跟进记录详情')
async def get_customer_follow_record_detail(
    request: Request,
    record_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    获取客户跟进记录详情

    :param request: 请求对象
    :param record_id: 跟进记录ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户跟进记录详情
    """
    follow_record = await CustomerFollowRecordService.get_customer_follow_record_detail_services(db, record_id)
    return ResponseUtil.success(data=follow_record)


@router.post('', response_model=CustomerFollowRecordModel, summary='新增客户跟进记录')
async def add_customer_follow_record(
    request: Request,
    follow_record: AddCustomerFollowRecordModel,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    新增客户跟进记录

    :param request: 请求对象
    :param follow_record: 新增客户跟进记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    result = await CustomerFollowRecordService.add_customer_follow_record_services(db, request, follow_record, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=CustomerFollowRecordModel, summary='编辑客户跟进记录')
async def edit_customer_follow_record(
    request: Request,
    follow_record: EditCustomerFollowRecordModel,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    编辑客户跟进记录

    :param request: 请求对象
    :param follow_record: 编辑客户跟进记录对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    result = await CustomerFollowRecordService.edit_customer_follow_record_services(db, request, follow_record, current_user)
    return ResponseUtil.success(data=result)


@router.delete('/{record_ids}', summary='删除客户跟进记录')
async def delete_customer_follow_record(
    request: Request,
    record_ids: str,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(LoginService.get_current_user),
):
    """
    删除客户跟进记录

    :param request: 请求对象
    :param record_ids: 跟进记录ID字符串
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    result = await CustomerFollowRecordService.delete_customer_follow_record_services(db, request, record_ids, current_user)
    return ResponseUtil.success(data=result)
