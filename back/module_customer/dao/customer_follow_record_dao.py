from datetime import datetime
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from typing import List
from module_customer.entity.do.customer_follow_record_do import CustomerFollowRecord
from module_customer.entity.vo.customer_follow_record_vo import CustomerFollowRecordPageQueryModel, CustomerFollowRecordQueryModel
from utils.page_util import PageUtil


class CustomerFollowRecordDao:
    """
    客户跟进记录数据库操作层
    """

    @classmethod
    async def get_customer_follow_record_list(cls, query_db: AsyncSession, query_object: CustomerFollowRecordQueryModel):
        """
        根据查询参数获取客户跟进记录列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 客户跟进记录列表
        """
        query = select(CustomerFollowRecord).options(selectinload(CustomerFollowRecord.follow_user))

        # 构建查询条件
        conditions = []

        if query_object.customer_id:
            conditions.append(CustomerFollowRecord.customer_id == query_object.customer_id)
        if query_object.follow_content:
            conditions.append(CustomerFollowRecord.follow_content.like(f'%{query_object.follow_content}%'))
        if query_object.follow_user_id:
            conditions.append(CustomerFollowRecord.follow_user_id == query_object.follow_user_id)
        if query_object.begin_time:
            conditions.append(CustomerFollowRecord.follow_time >= query_object.begin_time)
        if query_object.end_time:
            conditions.append(CustomerFollowRecord.follow_time <= query_object.end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 按跟进时间倒序排列
        query = query.order_by(desc(CustomerFollowRecord.follow_time))

        result = await query_db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_customer_follow_record_page_list(cls, query_db: AsyncSession, query_object: CustomerFollowRecordPageQueryModel):
        """
        根据查询参数分页获取客户跟进记录列表

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 客户跟进记录分页列表
        """
        query = select(CustomerFollowRecord).options(selectinload(CustomerFollowRecord.follow_user))

        # 构建查询条件
        conditions = []

        if query_object.customer_id:
            conditions.append(CustomerFollowRecord.customer_id == query_object.customer_id)
        if query_object.follow_content:
            conditions.append(CustomerFollowRecord.follow_content.like(f'%{query_object.follow_content}%'))
        if query_object.follow_user_id:
            conditions.append(CustomerFollowRecord.follow_user_id == query_object.follow_user_id)
        if query_object.begin_time:
            conditions.append(CustomerFollowRecord.follow_time >= query_object.begin_time)
        if query_object.end_time:
            conditions.append(CustomerFollowRecord.follow_time <= query_object.end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 按跟进时间倒序排列
        query = query.order_by(desc(CustomerFollowRecord.follow_time))

        return await PageUtil.paginate(query_db, query, query_object.page_num, query_object.page_size)

    @classmethod
    async def get_customer_follow_record_detail_by_id(cls, query_db: AsyncSession, record_id: int):
        """
        根据跟进记录ID获取跟进记录详情

        :param query_db: orm对象
        :param record_id: 跟进记录ID
        :return: 跟进记录详情
        """
        query = select(CustomerFollowRecord).options(selectinload(CustomerFollowRecord.follow_user)).where(CustomerFollowRecord.id == record_id)
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_customer_follow_record_dao(cls, query_db: AsyncSession, customer_follow_record_do: CustomerFollowRecord):
        """
        新增客户跟进记录

        :param query_db: orm对象
        :param customer_follow_record_do: 客户跟进记录对象
        :return: 新增结果
        """
        query_db.add(customer_follow_record_do)
        await query_db.flush()
        return customer_follow_record_do

    @classmethod
    async def edit_customer_follow_record_dao(cls, query_db: AsyncSession, customer_follow_record: CustomerFollowRecord):
        """
        编辑客户跟进记录

        :param query_db: orm对象
        :param customer_follow_record: 客户跟进记录对象
        :return: 编辑结果
        """
        await query_db.merge(customer_follow_record)
        await query_db.flush()
        return customer_follow_record

    @classmethod
    async def delete_customer_follow_record_dao(cls, query_db: AsyncSession, record_ids: List[int]):
        """
        删除客户跟进记录

        :param query_db: orm对象
        :param record_ids: 跟进记录ID列表
        :return: 删除结果
        """
        query = delete(CustomerFollowRecord).where(CustomerFollowRecord.id.in_(record_ids))
        result = await query_db.execute(query)
        return result.rowcount
