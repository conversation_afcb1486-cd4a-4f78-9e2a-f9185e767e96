from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from utils.page_util import as_query
from module_admin.entity.vo.user_vo import UserModel


class CustomerFollowRecordModel(BaseModel):
    """
    客户跟进记录模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='跟进记录ID')
    customer_id: Optional[int] = Field(default=None, description='客户ID')
    follow_content: Optional[str] = Field(default=None, description='跟进内容')
    follow_time: Optional[datetime] = Field(default=None, description='跟进时间')
    follow_user_id: Optional[int] = Field(default=None, description='跟进人用户ID')
    create_user_id: Optional[int] = Field(default=None, description='创建人用户ID')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    follow_user: Optional[UserModel] = Field(default=None, description='跟进人信息')
    create_user: Optional[UserModel] = Field(default=None, description='创建人信息')


class CustomerFollowRecordQueryModel(BaseModel):
    """
    客户跟进记录查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    customer_id: Optional[int] = Field(default=None, description='客户ID')
    follow_content: Optional[str] = Field(default=None, description='跟进内容')
    follow_user_id: Optional[int] = Field(default=None, description='跟进人用户ID')
    create_user_id: Optional[int] = Field(default=None, description='创建人用户ID')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class CustomerFollowRecordPageQueryModel(CustomerFollowRecordQueryModel):
    """
    客户跟进记录分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class AddCustomerFollowRecordModel(BaseModel):
    """
    新增客户跟进记录模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    customer_id: int = Field(description='客户ID')
    follow_content: str = Field(description='跟进内容')
    follow_time: datetime = Field(description='跟进时间')
    follow_user_id: Optional[int] = Field(default=None, description='跟进人用户ID（可选，默认为当前用户）')


class EditCustomerFollowRecordModel(AddCustomerFollowRecordModel):
    """
    编辑客户跟进记录模型
    """
    id: int = Field(description='跟进记录ID')
