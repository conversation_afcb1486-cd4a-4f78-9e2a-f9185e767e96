from datetime import datetime
from sqlalchemy import BigInteger, Column, DateTime, String, Text, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class CustomerFollowRecord(Base):
    """
    客户跟进记录表
    """
    __tablename__ = 'customer_follow_record'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='跟进记录ID')
    customer_id = Column(BigInteger, ForeignKey('customer.customer_id'), nullable=False, comment='客户ID')
    follow_content = Column(Text, nullable=False, comment='跟进内容')
    follow_time = Column(DateTime, nullable=False, comment='跟进时间')
    create_by = Column(String(64), default='', comment='跟进人（创建者）')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 关联关系
    customer = relationship("Customer", back_populates="follow_records")
