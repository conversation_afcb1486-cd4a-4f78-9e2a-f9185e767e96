-- ----------------------------
-- 1、客户表（包含集团和公司）
-- ----------------------------
drop table if exists customer;
create table customer (
  customer_id        bigint(20)      not null auto_increment    comment '客户ID',
  customer_name      varchar(100)    not null                   comment '客户名称',
  customer_level     char(1)         not null                   comment '客户级别（1集团 2公司）',
  parent_id          bigint(20)      default null               comment '上级客户ID',
  customer_type      varchar(20)     not null                   comment '客户类型（政府/企业/园区）',
  customer_source    varchar(50)     not null                   comment '客户来源',
  province           varchar(50)     not null                   comment '省份',
  city               varchar(50)     not null                   comment '城市',
  district           varchar(50)     default null               comment '区县',
  address            varchar(255)    default null               comment '详细地址',
  status             char(1)         default '0'                comment '状态（0正常 1停用）',
  del_flag           char(1)         default '0'                comment '删除标志（0存在 2删除）',
  create_by          varchar(64)     default ''                 comment '创建者',
  create_time        datetime        default null               comment '创建时间',
  update_by          varchar(64)     default ''                 comment '更新者',
  update_time        datetime        default null               comment '更新时间',
  remark             varchar(500)    default null               comment '备注',
  primary key (customer_id)
) engine=innodb auto_increment=100 comment = '客户表';

-- 添加唯一索引
create unique index idx_customer_name on customer(customer_name);
-- 添加普通索引
create index idx_parent_id on customer(parent_id);
create index idx_customer_level on customer(customer_level);
create index idx_customer_type on customer(customer_type);
create index idx_customer_source on customer(customer_source);
create index idx_province_city on customer(province, city);

-- ----------------------------
-- 2、客户联系人表
-- ----------------------------
drop table if exists customer_contact;
create table customer_contact (
  contact_id         bigint(20)      not null auto_increment    comment '联系人ID',
  customer_id        bigint(20)      not null                   comment '客户ID',
  contact_name       varchar(50)     not null                   comment '联系人姓名',
  position           varchar(50)     default null               comment '职务',
  phone              varchar(20)     default null               comment '电话',
  wechat             varchar(50)     default null               comment '微信',
  email              varchar(100)    default null               comment '邮箱',
  is_primary         char(1)         default '0'                comment '是否主要联系人（0否 1是）',
  status             char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by          varchar(64)     default ''                 comment '创建者',
  create_time        datetime        default null               comment '创建时间',
  update_by          varchar(64)     default ''                 comment '更新者',
  update_time        datetime        default null               comment '更新时间',
  remark             varchar(500)    default null               comment '备注',
  primary key (contact_id)
) engine=innodb auto_increment=100 comment = '客户联系人表';

-- 添加外键索引
create index idx_customer_id on customer_contact(customer_id);
-- 添加普通索引
create index idx_contact_name on customer_contact(contact_name);
create index idx_phone on customer_contact(phone);
create index idx_email on customer_contact(email);

-- ----------------------------
-- 3、客户内部负责人关联表
-- ----------------------------
drop table if exists customer_internal_manager;
create table customer_internal_manager (
  id                 bigint(20)      not null auto_increment    comment '关联ID',
  customer_id        bigint(20)      not null                   comment '客户ID',
  user_id            bigint(20)      not null                   comment '内部负责人ID',
  is_primary         char(1)         default '0'                comment '是否主要负责人（0否 1是）',
  create_by          varchar(64)     default ''                 comment '创建者',
  create_time        datetime        default null               comment '创建时间',
  update_by          varchar(64)     default ''                 comment '更新者',
  update_time        datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=100 comment = '客户内部负责人关联表';

-- 添加外键索引
create index idx_customer_id on customer_internal_manager(customer_id);
-- 添加普通索引
create index idx_user_id on customer_internal_manager(user_id);

-- ----------------------------
-- 4、客户跟进记录表
-- ----------------------------
drop table if exists customer_follow_record;
create table customer_follow_record (
  id                 bigint(20)      not null auto_increment    comment '跟进记录ID',
  customer_id        bigint(20)      not null                   comment '客户ID',
  follow_content     text            not null                   comment '跟进内容',
  follow_time        datetime        not null                   comment '跟进时间',
  follow_user_id     bigint(20)      not null                   comment '跟进人用户ID',
  create_by          varchar(64)     default ''                 comment '创建者',
  create_time        datetime        default null               comment '创建时间',
  update_by          varchar(64)     default ''                 comment '更新者',
  update_time        datetime        default null               comment '更新时间',
  primary key (id)
) engine=innodb auto_increment=100 comment = '客户跟进记录表';

-- 添加外键索引
create index idx_customer_id on customer_follow_record(customer_id);
create index idx_follow_user_id on customer_follow_record(follow_user_id);
-- 添加普通索引
create index idx_follow_time on customer_follow_record(follow_time);
create index idx_create_time on customer_follow_record(create_time);
